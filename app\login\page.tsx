"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   checkFirstTimeSetup,
   loginAction,
   performFirstTimeSetup,
} from "@/lib/actions/auth-actions";
import { LockClosedIcon } from "@heroicons/react/24/solid";
import { Eye, EyeOff, Shield } from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { toast } from "sonner";

function LoginForm() {
   const [password, setPassword] = useState("");
   const [confirmPassword, setConfirmPassword] = useState("");
   const [showPassword, setShowPassword] = useState(false);
   const [showConfirmPassword, setShowConfirmPassword] = useState(false);
   const [isLoading, setIsLoading] = useState(false);
   const [needsSetup, setNeedsSetup] = useState(false);
   const [checkingSetup, setCheckingSetup] = useState(true);

   const router = useRouter();
   const searchParams = useSearchParams();
   const redirectTo = searchParams.get("redirect") || "/admin";

   // Check if first-time setup is needed
   useEffect(() => {
      async function checkSetup() {
         try {
            const result = await checkFirstTimeSetup();
            if (result.success && result.data) {
               setNeedsSetup(result.data.needsSetup);
            }
         } catch (error) {
            console.error("Error checking setup:", error);
            toast.error("Failed to check setup status");
         } finally {
            setCheckingSetup(false);
         }
      }

      checkSetup();
   }, []);

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setIsLoading(true);

      try {
         if (needsSetup) {
            // First-time setup
            if (password !== confirmPassword) {
               toast.error("Passwords do not match");
               return;
            }

            if (password.length < 6) {
               toast.error("Password must be at least 6 characters long");
               return;
            }

            const result = await performFirstTimeSetup({ password });

            if (result.success) {
               toast.success("Admin account created successfully!");
               router.push(redirectTo);
            } else {
               toast.error(result.error || "Setup failed");
            }
         } else {
            // Regular login
            const result = await loginAction({ password });

            if (result.success) {
               toast.success("Login successful!");
               router.push(redirectTo);
            } else {
               toast.error(result.error || "Login failed");
            }
         }
      } catch (error) {
         console.error("Error during authentication:", error);
         toast.error("An unexpected error occurred");
      } finally {
         setIsLoading(false);
      }
   };

   if (checkingSetup) {
      return (
         <div className="min-h-screen bg-background flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
         </div>
      );
   }

   return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
         <div className="w-full max-w-md">
            {/* Logo and Header */}
            <div className="text-center mb-8">
               <div className="flex justify-center mb-6">
                  <Image
                     src="/astral-logo.svg"
                     alt="Astral Studios"
                     width={120}
                     height={60}
                     className="h-12 w-auto"
                  />
               </div>
               <h1 className="text-2xl font-bold text-foreground mb-2">
                  {needsSetup ? "Welcome to Astral Studios" : "Admin Login"}
               </h1>
               <p className="text-muted-foreground">
                  {needsSetup
                     ? "Set up your admin account to get started"
                     : "Enter your password to access the admin panel"}
               </p>
            </div>

            {/* Login Form */}
            <div className="bg-[#101010] border border-border/50 rounded-lg p-6 shadow-card">
               <form onSubmit={handleSubmit} className="space-y-4">
                  {needsSetup && (
                     <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6">
                        <div className="flex items-center gap-2 text-primary mb-2">
                           <Shield className="h-4 w-4" />
                           <span className="font-medium">First-Time Setup</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                           Create your admin password to secure your gallery
                           management system.
                        </p>
                     </div>
                  )}

                  {/* Password Field */}
                  <div className="space-y-4">
                     <Label htmlFor="password" className="text-foreground">
                        <LockClosedIcon className="h-4 w-4" />
                        {needsSetup ? "Create Password" : "Password"}
                     </Label>
                     <div className="relative">
                        <Input
                           id="password"
                           type={showPassword ? "text" : "password"}
                           value={password}
                           onChange={(e) => setPassword(e.target.value)}
                           placeholder={
                              needsSetup
                                 ? "Enter a secure password"
                                 : "Enter your password"
                           }
                           required
                           className="pr-10"
                           disabled={isLoading}
                        />
                        <button
                           type="button"
                           onClick={() => setShowPassword(!showPassword)}
                           className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                           disabled={isLoading}
                        >
                           {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                           ) : (
                              <Eye className="h-4 w-4" />
                           )}
                        </button>
                     </div>
                  </div>

                  {/* Confirm Password Field (only for setup) */}
                  {needsSetup && (
                     <div className="space-y-4">
                        <Label
                           htmlFor="confirmPassword"
                           className="text-foreground"
                        >
                           <LockClosedIcon className="h-4 w-4" />
                           Confirm Password
                        </Label>
                        <div className="relative">
                           <Input
                              id="confirmPassword"
                              type={showConfirmPassword ? "text" : "password"}
                              value={confirmPassword}
                              onChange={(e) =>
                                 setConfirmPassword(e.target.value)
                              }
                              placeholder="Confirm your password"
                              required
                              className="pr-10"
                              disabled={isLoading}
                           />
                           <button
                              type="button"
                              onClick={() =>
                                 setShowConfirmPassword(!showConfirmPassword)
                              }
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                              disabled={isLoading}
                           >
                              {showConfirmPassword ? (
                                 <EyeOff className="h-4 w-4" />
                              ) : (
                                 <Eye className="h-4 w-4" />
                              )}
                           </button>
                        </div>
                     </div>
                  )}

                  {/* Submit Button */}
                  <Button type="submit" className="w-full" disabled={isLoading}>
                     {isLoading ? (
                        <div className="flex items-center gap-2">
                           <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                           {needsSetup
                              ? "Creating Account..."
                              : "Signing In..."}
                        </div>
                     ) : needsSetup ? (
                        "Create Admin Account"
                     ) : (
                        "Sign In"
                     )}
                  </Button>
               </form>
            </div>

            {/* Footer */}
            <div className="text-center mt-6">
               <p className="text-sm text-muted-foreground">
                  © 2024 Astral Studios. All rights reserved.
               </p>
            </div>
         </div>
      </div>
   );
}

function LoginPageFallback() {
   return (
      <div className="min-h-screen bg-background flex items-center justify-center">
         <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
   );
}

export default function LoginPage() {
   return (
      <Suspense fallback={<LoginPageFallback />}>
         <LoginForm />
      </Suspense>
   );
}
