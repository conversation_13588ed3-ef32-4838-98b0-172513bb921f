import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
   const baseUrl = "http://astralstudios.co.uk";

   return {
      rules: [
         {
            userAgent: "*",
            allow: "/",
            disallow: [
               "/admin/",
               "/api/",
               "/private/",
               "/_next/",
               "/login",
            ],
            crawlDelay: 1,
         },
         {
            userAgent: "Googlebot",
            allow: "/",
            disallow: ["/admin/", "/api/", "/private/", "/login"],
         },
         {
            userAgent: "Bingbot",
            allow: "/",
            disallow: ["/admin/", "/api/", "/private/", "/login"],
         },
      ],
      sitemap: `${baseUrl}/sitemap.xml`,
      host: baseUrl,
   };
}
