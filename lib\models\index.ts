// Export all models and types
export * from "./admin";
export * from "./album";
export * from "./collection";
export * from "./comment";
export * from "./image";

// Common types and utilities
export interface PaginationOptions {
   page?: number;
   limit?: number;
   sortBy?: string;
   sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
   data: T[];
   pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
   };
}

export interface ApiResponse<T = unknown> {
   success: boolean;
   data?: T;
   error?: string;
   message?: string;
}

// Default pagination settings
export const DEFAULT_PAGINATION = {
   page: 1,
   limit: 20,
   sortBy: "createdAt",
   sortOrder: "desc" as const,
};

/**
 * Create pagination metadata
 */
export function createPaginationMetadata(
   page: number,
   limit: number,
   total: number
) {
   const totalPages = Math.ceil(total / limit);

   return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
   };
}
