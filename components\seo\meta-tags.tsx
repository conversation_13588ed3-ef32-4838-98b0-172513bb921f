import { defaultSEO, pageSEO, serviceSEO, type SEOConfig } from "@/data/seo";
import { Metadata } from "next";

interface MetaTagsProps {
   page?: keyof typeof pageSEO;
   service?: keyof typeof serviceSEO;
   customSEO?: Partial<SEOConfig>;
   baseUrl?: string;
}

/**
 * Generate comprehensive metadata for Next.js pages
 * Supports page-specific, service-specific, and custom SEO configurations
 */
export function generateMetadata({
   page,
   service,
   customSEO,
   baseUrl = "http://astralstudios.co.uk",
}: MetaTagsProps): Metadata {
   // Determine which SEO config to use
   let seoConfig: SEOConfig;

   if (service && serviceSEO[service]) {
      seoConfig = serviceSEO[service];
   } else if (page && pageSEO[page]) {
      seoConfig = pageSEO[page];
   } else {
      seoConfig = defaultSEO;
   }

   // Merge with custom SEO if provided
   if (customSEO) {
      seoConfig = {
         ...seoConfig,
         ...customSEO,
         openGraph: {
            ...seoConfig.openGraph,
            ...customSEO.openGraph,
         },
         twitter: {
            ...seoConfig.twitter,
            ...customSEO.twitter,
         },
         robots: {
            ...seoConfig.robots,
            ...customSEO.robots,
         },
      };
   }

   // Build canonical URL
   const canonicalUrl = seoConfig.canonical
      ? `${baseUrl}${seoConfig.canonical}`
      : baseUrl;

   // Generate metadata object
   const metadata: Metadata = {
      title: seoConfig.title,
      description: seoConfig.description,
      keywords: seoConfig.keywords?.join(", "),
      authors: [{ name: "Astral Studios" }],
      creator: "Astral Studios",
      publisher: "Astral Studios",
      metadataBase: new URL(baseUrl),
      alternates: {
         canonical: canonicalUrl,
      },
      robots: {
         index: seoConfig.robots?.index ?? true,
         follow: seoConfig.robots?.follow ?? true,
         noarchive: seoConfig.robots?.noarchive ?? false,
         nosnippet: seoConfig.robots?.nosnippet ?? false,
         googleBot: {
            index: seoConfig.robots?.index ?? true,
            follow: seoConfig.robots?.follow ?? true,
            "max-video-preview": -1,
            "max-image-preview": "large",
            "max-snippet": -1,
         },
      },
      openGraph: {
         title: seoConfig.openGraph?.title || seoConfig.title,
         description: seoConfig.openGraph?.description || seoConfig.description,
         url: canonicalUrl,
         siteName: "Astral Studios",
         type: "website",
         locale: "en_GB",
         images: seoConfig.openGraph?.image
            ? [
                 {
                    url: seoConfig.openGraph.image,
                    width: 1200,
                    height: 630,
                    alt: seoConfig.openGraph.title || seoConfig.title,
                 },
              ]
            : [
                 {
                    url: "/images/hero.JPG",
                    width: 1200,
                    height: 630,
                    alt: "Astral Studios - Professional Photography",
                 },
              ],
      },
      twitter: {
         card: "summary_large_image",
         title: seoConfig.twitter?.title || seoConfig.title,
         description: seoConfig.twitter?.description || seoConfig.description,
         images: seoConfig.twitter?.image
            ? [seoConfig.twitter.image]
            : ["/images/hero.JPG"],
         creator: "@astralstudios",
         site: "@astralstudios",
      },
      verification: {
         google: process.env.GOOGLE_SITE_VERIFICATION,
         yandex: process.env.YANDEX_VERIFICATION,
         yahoo: process.env.YAHOO_SITE_VERIFICATION,
      },
      category: "Photography and Videography Services",
   };

   return metadata;
}

/**
 * Hook for generating metadata in page components
 */
export function usePageMetadata(props: MetaTagsProps): Metadata {
   return generateMetadata(props);
}

/**
 * Utility function to generate JSON-LD structured data
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function generateStructuredData(type: string, data?: any) {
   const baseData = {
      "@context": "https://schema.org",
      "@type": type,
   };

   switch (type) {
      case "Organization":
         return {
            ...baseData,
            name: "Astral Studios",
            url: "http://astralstudios.co.uk",
            logo: "http://astralstudios.co.uk/astral-logo.svg",
            description:
               "Professional photography and videography services in the UK",
            email: "<EMAIL>",
            telephone: "+447886161245",
            address: {
               "@type": "PostalAddress",
               addressCountry: "GB",
               addressRegion: "United Kingdom",
            },
            sameAs: [
               "https://instagram.com/astralstudios",
               "https://facebook.com/astralstudios",
            ],
            ...data,
         };

      case "LocalBusiness":
         return {
            ...baseData,
            "@type": "ProfessionalService",
            name: "Astral Studios",
            description: "Professional photography and videography services",
            url: "http://astralstudios.co.uk",
            telephone: "+447886161245",
            email: "<EMAIL>",
            address: {
               "@type": "PostalAddress",
               addressCountry: "GB",
            },
            serviceArea: {
               "@type": "Country",
               name: "United Kingdom",
            },
            priceRange: "£150-£1000",
            ...data,
         };

      case "Service":
         return {
            ...baseData,
            serviceType: data?.serviceType || "Photography Services",
            provider: {
               "@type": "Organization",
               name: "Astral Studios",
               url: "http://astralstudios.co.uk",
            },
            areaServed: {
               "@type": "Country",
               name: "United Kingdom",
            },
            description:
               data?.description ||
               "Professional photography and videography services",
            offers: {
               "@type": "Offer",
               availability: "https://schema.org/InStock",
               price: data?.price || "Contact for pricing",
               priceCurrency: "GBP",
            },
            ...data,
         };

      case "WebSite":
         return {
            ...baseData,
            name: "Astral Studios",
            url: "http://astralstudios.co.uk",
            description: "Professional photography and videography services",
            publisher: {
               "@type": "Organization",
               name: "Astral Studios",
            },
            potentialAction: {
               "@type": "SearchAction",
               target:
                  "http://astralstudios.co.uk/search?q={search_term_string}",
               "query-input": "required name=search_term_string",
            },
            ...data,
         };

      default:
         return { ...baseData, ...data };
   }
}
