"use client";

import { cn } from "@/lib/utils";
import Image from "next/image";
import { useState } from "react";

interface OptimizedImageProps {
   src: string;
   alt: string;
   width?: number;
   height?: number;
   fill?: boolean;
   className?: string;
   priority?: boolean;
   sizes?: string;
   quality?: number;
   placeholder?: "blur" | "empty";
   blurDataURL?: string;
}

export function OptimizedImage({
   src,
   alt,
   width,
   height,
   fill = false,
   className = "",
   priority = false,
   sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
   quality = 85,
   placeholder = "empty",
   blurDataURL,
}: OptimizedImageProps) {
   const [isLoading, setIsLoading] = useState(true);
   const [hasError, setHasError] = useState(false);

   const handleLoad = () => {
      setIsLoading(false);
   };

   const handleError = () => {
      setIsLoading(false);
      setHasError(true);
   };

   if (hasError) {
      return (
         <div
            className={cn(
               "flex items-center justify-center bg-muted text-muted-foreground",
               className
            )}
         >
            <span className="text-sm">Image failed to load</span>
         </div>
      );
   }

   return (
      <div className={cn("relative overflow-hidden", className)}>
         {isLoading && (
            <div className="absolute inset-0 bg-muted animate-pulse" />
         )}
         <Image
            src={src}
            alt={alt}
            width={width}
            height={height}
            fill={fill}
            priority={priority}
            sizes={sizes}
            quality={quality}
            placeholder={placeholder}
            blurDataURL={blurDataURL}
            onLoad={handleLoad}
            onError={handleError}
            className={cn(
               "transition-opacity duration-300",
               isLoading ? "opacity-0" : "opacity-100",
               fill ? "object-cover" : ""
            )}
         />
      </div>
   );
}
