"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";

// Social media links
const socialLinks = [
   {
      name: "Instagram",
      href: "https://instagram.com/astralstudios",
      icon: (
         <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="#000000"
            viewBox="0 0 330 330"
         >
            <path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160ZM176,24H80A56.06,56.06,0,0,0,24,80v96a56.06,56.06,0,0,0,56,56h96a56.06,56.06,0,0,0,56-56V80A56.06,56.06,0,0,0,176,24Zm40,152a40,40,0,0,1-40,40H80a40,40,0,0,1-40-40V80A40,40,0,0,1,80,40h96a40,40,0,0,1,40,40ZM192,76a12,12,0,1,1-12-12A12,12,0,0,1,192,76Z"></path>
         </svg>
      ),
   },
   {
      name: "TikTok",
      href: "https://tiktok.com/@astralstudioz",
      icon: (
         <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 640 640"
            className="w-full h-full fill-current"
         >
            <path d="M544.5 273.9C500.5 274 457.5 260.3 421.7 234.7L421.7 413.4C421.7 446.5 411.6 478.8 392.7 506C373.8 533.2 347.1 554 316.1 565.6C285.1 577.2 251.3 579.1 219.2 570.9C187.1 562.7 158.3 545 136.5 520.1C114.7 495.2 101.2 464.1 97.5 431.2C93.8 398.3 100.4 365.1 116.1 336C131.8 306.9 156.1 283.3 185.7 268.3C215.3 253.3 248.6 247.8 281.4 252.3L281.4 342.2C266.4 337.5 250.3 337.6 235.4 342.6C220.5 347.6 207.5 357.2 198.4 369.9C189.3 382.6 184.4 398 184.5 413.8C184.6 429.6 189.7 444.8 199 457.5C208.3 470.2 221.4 479.6 236.4 484.4C251.4 489.2 267.5 489.2 282.4 484.3C297.3 479.4 310.4 469.9 319.6 457.2C328.8 444.5 333.8 429.1 333.8 413.4L333.8 64H421.8C421.7 71.4 422.4 78.9 423.7 86.2C426.8 102.5 433.1 118.1 442.4 131.9C451.7 145.7 463.7 157.5 477.6 166.5C497.5 179.6 520.8 186.6 544.6 186.6L544.6 274z" />
         </svg>
      ),
   },
   {
      name: "Gmail",
      href: "mailto:<EMAIL>",
      icon: (
         <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
            <path d="M125.4 128C91.5 128 64 155.5 64 189.4C64 190.3 64 191.1 64.1 192L64 192L64 448C64 483.3 92.7 512 128 512L512 512C547.3 512 576 483.3 576 448L576 192L575.9 192C575.9 191.1 576 190.3 576 189.4C576 155.5 548.5 128 514.6 128L125.4 128zM528 256.3L528 448C528 456.8 520.8 464 512 464L128 464C119.2 464 112 456.8 112 448L112 256.3L266.8 373.7C298.2 397.6 341.7 397.6 373.2 373.7L528 256.3zM112 189.4C112 182 118 176 125.4 176L514.6 176C522 176 528 182 528 189.4C528 193.6 526 197.6 522.7 200.1L344.2 335.5C329.9 346.3 310.1 346.3 295.8 335.5L117.3 200.1C114 197.6 112 193.6 112 189.4z" />
         </svg>
      ),
   },
];

export type SocialMediaVariant =
   | "floating"
   | "minimal"
   | "gradient"
   | "neon"
   | "elegant";

interface SocialMediaTagProps {
   variant?: SocialMediaVariant;
   className?: string;
}

export function SocialMediaTag({
   variant = "floating",
   className,
}: SocialMediaTagProps) {
   const baseClasses =
      "hidden lg:block absolute right-6 top-1/2 -translate-y-1/2 z-40";

   const variantStyles = {
      floating: {
         container:
            "flex flex-col space-y-3 p-4 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-lg",
         link: "w-12 h-12 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-all duration-300 hover:scale-110 hover:rotate-12",
         icon: "w-6 h-6",
      },
      minimal: {
         container: "flex flex-col space-y-2",
         link: "size-12 rounded-lg bg-black/50 hover:bg-black/70 flex items-center justify-center text-white transition-all duration-200 hover:scale-105",
         icon: "size-6",
      },
      gradient: {
         container:
            "flex flex-col space-y-3 p-3 bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-xl border border-gradient-accent/30",
         link: "w-12 h-12 rounded-full bg-gradient-accent hover:opacity-80 flex items-center justify-center text-white transition-all duration-300 hover:scale-110 shadow-lg",
         icon: "w-6 h-6",
      },
      neon: {
         container:
            "flex flex-col space-y-3 p-4 bg-black/80 rounded-lg border border-red-400/50 shadow-[0_0_20px_rgba(239,68,68,0.3)]",
         link: "w-12 h-12 rounded-full border-2 border-red-400 hover:border-red-300 flex items-center justify-center text-red-400 hover:text-red-300 transition-all duration-300 hover:scale-110 hover:shadow-[0_0_15px_rgba(239,68,68,0.5)]",
         icon: "w-6 h-6",
      },
      elegant: {
         container:
            "flex flex-col space-y-3 p-3 bg-astral-grey/90 backdrop-blur-sm rounded-lg border border-astral-grey-light/50 shadow-elegant",
         link: "w-11 h-11 rounded-xl bg-gradient-accent opacity-70 hover:bg-gradient-accent/20 flex items-center justify-center text-black transition-all duration-300 hover:scale-105",
         icon: "size-6",
      },
   };

   const styles = variantStyles[variant];

   return (
      <motion.div
         initial={{ opacity: 0, x: 50 }}
         animate={{ opacity: 1, x: 0 }}
         transition={{ duration: 0.4, delay: 1.2 }}
         className={cn(baseClasses, className)}
      >
         <div className={styles.container}>
            {socialLinks.map((link) => (
               <motion.a
                  key={link.name}
                  href={link.href}
                  target={link.name === "Gmail" ? "_self" : "_blank"}
                  rel={
                     link.name === "Gmail" ? undefined : "noopener noreferrer"
                  }
                  className={styles.link}
                  whileTap={{ scale: 0.95 }}
                  aria-label={`Follow us on ${link.name}`}
               >
                  <div className={styles.icon}>{link.icon}</div>
               </motion.a>
            ))}
         </div>
      </motion.div>
   );
}

export default SocialMediaTag;
