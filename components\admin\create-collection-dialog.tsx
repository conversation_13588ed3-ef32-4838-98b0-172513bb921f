"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

import { useCreateCollection } from "@/lib/hooks/use-collections";
import {
   CreateCollectionFormData,
   createCollectionSchema,
} from "@/lib/schemas/collection-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

interface CreateCollectionDialogProps {
   trigger?: React.ReactNode;
   onSuccess?: (collectionId: string) => void;
   open?: boolean;
   onOpenChange?: (open: boolean) => void;
   showTrigger?: boolean;
}

export default function CreateCollectionDialog({
   trigger,
   onSuccess,
   open: controlledOpen,
   onOpenChange: controlledOnOpenChange,
   showTrigger = false,
}: CreateCollectionDialogProps) {
   const [uncontrolledOpen, setUncontrolledOpen] = useState(false);
   const open =
      controlledOpen !== undefined ? controlledOpen : uncontrolledOpen;
   const setOpen = controlledOnOpenChange || setUncontrolledOpen;
   const [redirectChoice, setRedirectChoice] = useState<
      "redirect" | "close" | null
   >(null);
   const router = useRouter();
   const createCollectionMutation = useCreateCollection();

   const [selectedColor, setSelectedColor] = useState("#4d545c");

   const {
      register,
      handleSubmit,
      formState: { errors, isSubmitting },
      reset,
   } = useForm<CreateCollectionFormData>({
      resolver: zodResolver(createCollectionSchema),
      defaultValues: {
         name: "",
         description: "",
         color: "#4d545c",
      },
   });

   const onSubmit = async (data: CreateCollectionFormData) => {
      try {
         const result = await createCollectionMutation.mutateAsync({
            ...data,
            color: selectedColor,
         });

         if (result._id) {
            const collectionId = result._id.toString();

            if (onSuccess) {
               onSuccess(collectionId);
               setOpen(false);
               reset();
            } else {
               // Show redirect choice dialog
               setRedirectChoice("redirect");
            }
         }
      } catch (error) {
         // Error is handled by the mutation's onError callback
         console.error("Failed to create collection:", error);
      }
   };

   const handleRedirectChoice = (choice: "redirect" | "close") => {
      if (choice === "redirect" && createCollectionMutation.data?._id) {
         router.push(`/admin/collections/${createCollectionMutation.data._id}`);
      }

      setOpen(false);
      setRedirectChoice(null);
      reset();
   };

   const handleOpenChange = (newOpen: boolean) => {
      setOpen(newOpen);
      if (!newOpen) {
         setRedirectChoice(null);
         reset();
         setSelectedColor("#4d545c");
      }
   };

   return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
         <DialogTrigger asChild>
            {trigger ||
               (showTrigger ? (
                  <Button className="bg-gradient-accent hover:opacity-90">
                     <Plus className="w-4 h-4" />
                     New Collection
                  </Button>
               ) : null)}
         </DialogTrigger>
         <DialogContent className="sm:max-w-[425px]">
            {redirectChoice ? (
               <>
                  <DialogHeader>
                     <DialogTitle>Collection Created Successfully!</DialogTitle>
                     <DialogDescription className="text-sm leading-relaxed">
                        Your collection has been created. Would you like to go
                        to the collection page or stay here?
                     </DialogDescription>
                  </DialogHeader>
                  <DialogFooter className="flex-col sm:flex-row gap-2">
                     <Button
                        variant="outline"
                        onClick={() => handleRedirectChoice("close")}
                        className="w-full sm:w-auto"
                     >
                        Stay Here
                     </Button>
                     <Button
                        onClick={() => handleRedirectChoice("redirect")}
                        className="w-full sm:w-auto bg-gradient-accent hover:opacity-90"
                     >
                        Go to Collection
                     </Button>
                  </DialogFooter>
               </>
            ) : (
               <>
                  <DialogHeader>
                     <DialogTitle>Create New Collection</DialogTitle>
                     <DialogDescription>
                        Create a new collection to tag your images.
                     </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
                     <div className="space-y-3">
                        <Label htmlFor="name">Collection Name *</Label>
                        <Input
                           id="name"
                           placeholder="Enter collection name"
                           {...register("name")}
                           className={errors.name ? "border-destructive" : ""}
                        />
                        {errors.name && (
                           <p className="text-sm text-destructive">
                              {errors.name.message}
                           </p>
                        )}
                     </div>

                     <div className="space-y-3">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                           id="description"
                           placeholder="Enter collection description (optional)"
                           rows={3}
                           {...register("description")}
                           className={
                              errors.description ? "border-destructive" : ""
                           }
                        />
                        {errors.description && (
                           <p className="text-sm text-destructive">
                              {errors.description.message}
                           </p>
                        )}
                     </div>

                     <div className="space-y-3">
                        <Label htmlFor="color">Collection Color</Label>
                        <div className="flex items-center gap-3">
                           <div className="w-10 h-10 rounded border overflow-hidden">
                              <div
                                 className="w-full h-full"
                                 style={{ backgroundColor: selectedColor }}
                              />
                           </div>
                           <Input
                              type="color"
                              value={selectedColor}
                              onChange={(e) => setSelectedColor(e.target.value)}
                              className="flex-1 h-10"
                           />
                        </div>
                     </div>

                     <DialogFooter>
                        <Button
                           type="button"
                           variant="outline"
                           onClick={() => setOpen(false)}
                           disabled={isSubmitting}
                        >
                           Cancel
                        </Button>
                        <Button
                           type="submit"
                           disabled={isSubmitting}
                           className="bg-gradient-accent hover:opacity-90"
                        >
                           {isSubmitting ? (
                              <>
                                 <Loader2 className="w-4 h-4 animate-spin" />
                                 Creating...
                              </>
                           ) : (
                              "Create Collection"
                           )}
                        </Button>
                     </DialogFooter>
                  </form>
               </>
            )}
         </DialogContent>
      </Dialog>
   );
}
