"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Image as ImageType } from "@/lib/models";
import {
   ArchiveBoxArrowDownIcon,
   PhotoIcon,
   TagIcon,
   TrashIcon,
} from "@heroicons/react/24/solid";
import { Download, MoreVertical } from "lucide-react";
import * as React from "react";

interface ImageActionMenuProps {
   image: ImageType;
   onAction: (action: string, imageId: string) => void;
   showSetCover?: boolean;
   showMoveToAlbum?: boolean;
   showAddToCollection?: boolean;
   className?: string;
}

export default function ImageActionMenu({
   image,
   onAction,
   showSetCover = false,
   showMoveToAlbum = true,
   showAddToCollection = true,
   className,
}: ImageActionMenuProps) {
   const [open, setOpen] = React.useState(false);

   const handleAction = async (action: string) => {
      if (action === "download") {
         try {
            // Fetch the image as a blob to force download
            const response = await fetch(image.url);
            if (!response.ok) {
               throw new Error("Failed to fetch image");
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            // Create download link
            const link = document.createElement("a");
            link.href = url;
            link.download = image.name || `image_${image._id}.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL
            URL.revokeObjectURL(url);
         } catch (error) {
            console.error("Error downloading image:", error);
            // Fallback to direct link method
            const link = document.createElement("a");
            link.href = image.url;
            link.download = image.name || `image_${image._id}.jpg`;
            link.target = "_blank";
            link.rel = "noopener noreferrer";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
         }
      } else {
         onAction(action, image._id!);
      }
      setOpen(false); // Close the dropdown menu after an action is triggered
   };

   return (
      <DropdownMenu open={open} onOpenChange={setOpen}>
         <DropdownMenuTrigger asChild>
            <Button
               variant="outline"
               size="sm"
               className={`h-8 w-8 rounded-lg p-0 bg-background/80 backdrop-blur-sm border-none hover:bg-background/90 ${className}`}
            >
               <MoreVertical className="w-4 h-4" />
               <span className="sr-only">Open menu</span>
            </Button>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end" className="w-48">
            {showSetCover && (
               <>
                  <DropdownMenuItem onClick={() => handleAction("set-cover")}>
                     <PhotoIcon className="w-4 h-4 mr-2" />
                     Set as Cover
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
               </>
            )}

            {showMoveToAlbum && (
               <DropdownMenuItem onClick={() => handleAction("move-to-album")}>
                  <ArchiveBoxArrowDownIcon className="w-4 h-4 mr-2" />
                  Move to Album
               </DropdownMenuItem>
            )}

            {showAddToCollection && (
               <DropdownMenuItem
                  onClick={() => handleAction("add-to-collection")}
               >
                  <TagIcon className="w-4 h-4 mr-2" />
                  Add to Collection
               </DropdownMenuItem>
            )}

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={() => handleAction("download")}>
               <Download className="w-4 h-4 mr-2" />
               Download Image
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
               onClick={() => handleAction("delete")}
               className="text-destructive font-semibold hover:!text-white"
            >
               <TrashIcon className="w-4 h-4 mr-2" />
               Delete Image
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}
