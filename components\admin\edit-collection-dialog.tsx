"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   <PERSON><PERSON>,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useUpdateCollection } from "@/lib/hooks/use-collections";
import type { CollectionWithStats } from "@/lib/models/collection";
import {
   UpdateCollectionFormData,
   updateCollectionSchema,
} from "@/lib/schemas/collection-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";

interface EditCollectionDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   collection: CollectionWithStats;
   onSuccess?: () => void;
}

export default function EditCollectionDialog({
   open,
   onOpenChange,
   collection,
   onSuccess,
}: EditCollectionDialogProps) {
   const [selectedColor, setSelectedColor] = useState(
      collection.color || "#4d545c"
   );
   const updateCollectionMutation = useUpdateCollection();

   const {
      register,
      handleSubmit,
      formState: { errors, isSubmitting },
      reset,
      setValue,
   } = useForm<UpdateCollectionFormData>({
      resolver: zodResolver(updateCollectionSchema),
      defaultValues: {
         name: collection.name || "",
         description: collection.description || "",
         color: collection.color || "",
      },
   });

   // Update form values if collection changes
   useEffect(() => {
      setValue("name", collection.name || "");
      setValue("description", collection.description || "");
      setValue("color", collection.color || "#4d545c");
      setSelectedColor(collection.color || "#4d545c");
   }, [collection, setValue]);

   const onSubmit = async (data: UpdateCollectionFormData) => {
      try {
         await updateCollectionMutation.mutateAsync({
            id: collection._id as string,
            input: {
               ...data,
               color: selectedColor,
            },
         });
         if (onSuccess) onSuccess();
         onOpenChange(false);
         reset();
      } catch (error) {
         console.error(error);
         // Error handled by mutation
      }
   };

   const handleDialogOpenChange = (newOpen: boolean) => {
      onOpenChange(newOpen);
      if (!newOpen) reset();
   };

   return (
      <Dialog open={open} onOpenChange={handleDialogOpenChange}>
         <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
               <DialogTitle>Edit Collection</DialogTitle>
               <DialogDescription>
                  Edit collection details below.
               </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
               <div className="space-y-3">
                  <Label htmlFor="edit-collection-name">
                     Collection Name *
                  </Label>
                  <Input
                     id="edit-collection-name"
                     {...register("name")}
                     className={errors.name ? "border-destructive" : ""}
                     required
                  />
                  {errors.name && (
                     <p className="text-sm text-destructive">
                        {errors.name.message}
                     </p>
                  )}
               </div>
               <div className="space-y-3">
                  <Label htmlFor="edit-collection-description">
                     Description
                  </Label>
                  <Textarea
                     id="edit-collection-description"
                     rows={3}
                     {...register("description")}
                     className={errors.description ? "border-destructive" : ""}
                  />
                  {errors.description && (
                     <p className="text-sm text-destructive">
                        {errors.description.message}
                     </p>
                  )}
               </div>
               <div className="space-y-3">
                  <Label htmlFor="edit-collection-color">
                     Collection Color
                  </Label>
                  <div className="flex items-center gap-3">
                     <div className="w-10 h-10 rounded border overflow-hidden">
                        <div
                           className="w-full h-full"
                           style={{ backgroundColor: selectedColor }}
                        />
                     </div>
                     <Input
                        type="color"
                        value={selectedColor}
                        onChange={(e) => setSelectedColor(e.target.value)}
                        className="flex-1 h-10"
                     />
                  </div>
               </div>
               <DialogFooter>
                  <Button
                     type="button"
                     variant="outline"
                     onClick={() => onOpenChange(false)}
                     disabled={
                        isSubmitting || updateCollectionMutation.isPending
                     }
                  >
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     disabled={
                        isSubmitting || updateCollectionMutation.isPending
                     }
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     {updateCollectionMutation.isPending ? (
                        <>
                           <Loader2 className="w-4 h-4 animate-spin" />{" "}
                           Saving...
                        </>
                     ) : (
                        "Save Changes"
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}
