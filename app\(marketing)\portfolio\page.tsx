"use client";

import ElementReveal from "@/components/animations/element-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { ImageGallery } from "@/components/image-gallery";
import { Button } from "@/components/ui/button";
import { categories, portfolioImages } from "@/data/portfolio";
import Lenis from "@studio-freight/lenis";
import { ArrowRight } from "lucide-react";
import { motion } from "motion/react";
import Link from "next/link";
import { useEffect, useState } from "react";

const getAllImages = () => {
   return Object.values(portfolioImages).flat();
};

export default function PortfolioPage() {
   const [activeCategory, setActiveCategory] = useState("all");

   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   const getFilteredImages = () => {
      if (activeCategory === "all") {
         return getAllImages();
      }
      return (
         portfolioImages[activeCategory as keyof typeof portfolioImages] || []
      );
   };

   const filteredImages = getFilteredImages();

   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="pt-36 pb-16 bg-gradient-hero">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
               <TextReveal>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-18">
                     Our <span className="text-primary">Portfolio</span>
                  </h1>
               </TextReveal>
               <TextReveal className="mb-8">
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                     Explore our collection of beautiful photography and
                     videography work. Each image tells a story, captures an
                     emotion, and preserves a precious moment in time.
                  </p>
               </TextReveal>
               <ElementReveal>
                  <Button asChild size="lg">
                     <Link href="/contact">Start Your Project</Link>
                  </Button>
               </ElementReveal>
            </div>
         </section>

         {/* Filter Categories */}
         <section className="py-12 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="flex flex-wrap justify-center gap-4">
                  {categories.map((category) => (
                     <motion.div
                        key={category.id}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                        viewport={{ once: true }}
                     >
                        <Button
                           key={category.id}
                           size={"lg"}
                           variant={
                              activeCategory === category.id
                                 ? "default"
                                 : "outline"
                           }
                           onClick={() => setActiveCategory(category.id)}
                           className={`flex items-center gap-2 ${
                              activeCategory === category.id
                                 ? "bg-gradient-accent"
                                 : ""
                           }`}
                        >
                           {category.name}
                        </Button>
                     </motion.div>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery */}
         <section className="py-20 pt-12">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-12">
                  <TextReveal>
                     <h2 className="text-2xl font-bold mb-4">
                        {categories.find((cat) => cat.id === activeCategory)
                           ?.name || "All Work"}
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-muted-foreground">
                        {filteredImages.length}{" "}
                        {filteredImages.length === 1 ? "image" : "images"} in
                        this category
                     </p>
                  </TextReveal>
               </div>

               <ImageGallery images={filteredImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                        Let&apos;s Create Your{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Next Masterpiece{" "}
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let&apos;s create something beautiful together. Contact
                        us to discuss your photography and videography needs.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/services">View Services</Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
}
