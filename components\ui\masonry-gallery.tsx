"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Image as ImageType } from "@/lib/models";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "./dialog";
import ImageActionMenu from "./image-action-menu";

interface MasonryGalleryProps {
   images: ImageType[];
   selectedImages?: Set<string>;
   onImageSelect?: (imageId: string, selected: boolean) => void;
   onImageAction?: (imageId: string, action: string) => void;
   showActions?: boolean;
   showSetCover?: boolean;
   showMoveToAlbum?: boolean;
   showAddToCollection?: boolean;
   showCheckboxes?: boolean;
   className?: string;
   // Infinite scroll props
   hasNextPage?: boolean;
   isFetchingNextPage?: boolean;
   fetchNextPage?: () => void;
   isLoading?: boolean;
   // Styles
   columns?: number;
   gap?: number;
}

interface ImageModalProps {
   image: ImageType | null;
   isOpen: boolean;
   onClose: () => void;
}

function ImageModal({ image, isOpen, onClose }: ImageModalProps) {
   // Calculate dynamic modal dimensions based on image aspect ratio
   const modalDimensions = useMemo(() => {
      if (!image) return;

      // Check if we're on the client side
      if (typeof window === "undefined") {
         return { width: 800, height: 600 }; // Fallback for SSR
      }

      const imageAspectRatio = image.width / image.height;
      const maxWidth = Math.min(
         window.innerWidth * 0.95,
         window.innerWidth - 40
      ); // 95vw with min 40px margin
      const maxHeight = Math.min(
         window.innerHeight * 0.95,
         window.innerHeight - 40
      ); // 95vh with min 40px margin

      // Calculate dimensions that maintain aspect ratio while fitting within viewport
      let modalWidth = image.width;
      let modalHeight = image.height;

      // Scale down if image is larger than viewport constraints
      if (modalWidth > maxWidth) {
         modalWidth = maxWidth;
         modalHeight = modalWidth / imageAspectRatio;
      }

      if (modalHeight > maxHeight) {
         modalHeight = maxHeight;
         modalWidth = modalHeight * imageAspectRatio;
      }

      // Ensure minimum enlargement - modal should be at least 1.5x the thumbnail size
      // Assuming thumbnail max size is around 300px, ensure modal is at least 450px in the larger dimension
      const minDimension = 450;
      const currentLargerDimension = Math.max(modalWidth, modalHeight);

      if (currentLargerDimension < minDimension) {
         const scaleFactor = minDimension / currentLargerDimension;
         modalWidth *= scaleFactor;
         modalHeight *= scaleFactor;

         // Re-check viewport constraints after scaling up
         if (modalWidth > maxWidth) {
            modalWidth = maxWidth;
            modalHeight = modalWidth / imageAspectRatio;
         }

         if (modalHeight > maxHeight) {
            modalHeight = maxHeight;
            modalWidth = modalHeight * imageAspectRatio;
         }
      }

      return {
         width: Math.round(modalWidth),
         height: Math.round(modalHeight),
      };
   }, [image]); // Recalculate when image changes

   if (!image || !modalDimensions) return null;

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent
            className="p-0 border-0 bg-transparent shadow-2xl rounded-xl overflow-hidden"
            style={{
               width: `${modalDimensions.width}px`,
               height: `${modalDimensions.height}px`,
               maxWidth: "95vw",
               maxHeight: "95vh",
            }}
         >
            <DialogHeader className="hidden sm:block absolute top-0 left-0 right-0 z-10 p-4">
               <DialogTitle className="text-white text-sm">
                  {image.name}
               </DialogTitle>
               <DialogDescription className="text-gray-100 text-xs font-medium">
                  {image.width} × {image.height} •{" "}
                  {(image.fileSize / 1024 / 1024).toFixed(2)} MB
               </DialogDescription>
            </DialogHeader>
            <div
               className="relative w-full h-full bg-black"
               style={{
                  width: `${modalDimensions.width}px`,
                  height: `${modalDimensions.height}px`,
               }}
            >
               <Image
                  src={image.url}
                  alt={image.name}
                  width={modalDimensions.width}
                  height={modalDimensions.height}
                  className="object-contain w-full h-full"
                  sizes="95vw"
                  quality={100}
                  priority
               />
            </div>
         </DialogContent>
      </Dialog>
   );
}

export default function MasonryGallery({
   images,
   selectedImages = new Set(),
   onImageSelect,
   onImageAction,
   showActions = true,
   showSetCover = false,
   showMoveToAlbum = true,
   showAddToCollection = true,
   showCheckboxes = false,
   className,
   // Infinite scroll props
   hasNextPage = false,
   isFetchingNextPage = false,
   fetchNextPage,
   isLoading = false,
   // Styles
   columns = 4,
   gap = 16,
}: MasonryGalleryProps) {
   const [selectedImage, setSelectedImage] = useState<ImageType | null>(null);
   const [isModalOpen, setIsModalOpen] = useState(false);
   const observerRef = useRef<IntersectionObserver | null>(null);
   const loadingRef = useRef<HTMLDivElement>(null);
   const [responsiveColumns, setResponsiveColumns] = useState<number>(columns);

   useEffect(() => {
      const getResponsiveColumns = () => {
         if (typeof window === "undefined") return columns;

         const width = window.innerWidth;
         if (width < 640) return 1;
         if (width < 768) return Math.min(2, columns);
         if (width < 1024) return Math.min(3, columns);
         return columns;
      };

      const handleResize = () => {
         setResponsiveColumns(getResponsiveColumns());
      };

      handleResize();
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
   }, [columns]);

   // Calculate which column each image should go in
   const getColumnIndex = (index: number, totalColumns: number) => {
      return index % totalColumns;
   };

   // Create columns array
   const columnsArray = Array.from(
      { length: responsiveColumns },
      () => [] as ImageType[]
   );

   // Distribute images across columns
   images.forEach((image, index) => {
      const columnIndex = getColumnIndex(index, responsiveColumns);
      if (columnsArray[columnIndex]) {
         columnsArray[columnIndex].push(image);
      }
   });

   const handleImageClick = (image: ImageType) => {
      setSelectedImage(image);
      setIsModalOpen(true);
   };

   const handleCloseModal = () => {
      setIsModalOpen(false);
      setSelectedImage(null);
   };

   const handleCheckboxChange = (imageId: string, checked: boolean) => {
      onImageSelect?.(imageId, checked);
   };

   // Infinite scroll observer
   const handleObserver = useCallback(
      (entries: IntersectionObserverEntry[]) => {
         const [target] = entries;
         if (
            target.isIntersecting &&
            hasNextPage &&
            !isFetchingNextPage &&
            fetchNextPage
         ) {
            fetchNextPage();
         }
      },
      [hasNextPage, isFetchingNextPage, fetchNextPage]
   );

   useEffect(() => {
      const element = loadingRef.current;
      if (!element) return;

      observerRef.current = new IntersectionObserver(handleObserver, {
         rootMargin: "100px", // Start loading 100px before reaching the bottom
      });

      observerRef.current.observe(element);

      return () => {
         if (observerRef.current) {
            observerRef.current.disconnect();
         }
      };
   }, [handleObserver]);

   if (isLoading) {
      return (
         <div
            className={cn("flex items-center justify-center h-64", className)}
         >
            <div className="flex items-center space-x-2">
               <Loader2 className="h-6 w-6 animate-spin" />
               <span className="text-muted-foreground">Loading images...</span>
            </div>
         </div>
      );
   }

   if (images.length === 0) {
      return (
         <div
            className={cn(
               "flex items-center justify-center h-64 text-muted-foreground",
               className
            )}
         >
            <div className="text-center">
               <p>No images to display</p>
            </div>
         </div>
      );
   }

   return (
      <>
         <div className="w-full">
            <div className="flex items-start" style={{ gap }}>
               {columnsArray.map((columnImages, columnIndex) => (
                  <div
                     key={columnIndex}
                     className="flex-1 flex flex-col"
                     style={{ gap }}
                  >
                     {columnImages.map((image) => (
                        <div
                           key={image._id}
                           className="group relative cursor-pointer"
                           onClick={() => handleImageClick(image)}
                        >
                           <div className="relative overflow-hidden rounded-lg bg-astral-grey/90 h-full">
                              <Image
                                 src={image.url}
                                 alt={image.name}
                                 width={image.width}
                                 height={image.height}
                                 className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                                 sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                              />

                              {/* Overlay */}
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />

                              {/* Checkbox - Top Left */}
                              {showCheckboxes && (
                                 <div
                                    className={cn(
                                       "absolute top-2 left-2 z-10 transition-opacity duration-200",
                                       // On mobile: always visible
                                       // On desktop: hidden by default, visible on hover or when selected
                                       selectedImages.size > 0
                                          ? "opacity-100"
                                          : "opacity-100 md:opacity-0 md:group-hover:opacity-100"
                                    )}
                                    onClick={(e) => e.stopPropagation()}
                                 >
                                    <Checkbox
                                       checked={selectedImages.has(image._id!)}
                                       onCheckedChange={(checked) =>
                                          handleCheckboxChange(
                                             image._id!,
                                             checked as boolean
                                          )
                                       }
                                       className="bg-background/80 size-5 rounded-sm backdrop-blur-sm border-2"
                                    />
                                 </div>
                              )}

                              {/* Actions Menu */}
                              {showActions && (
                                 <div
                                    className="absolute top-2 right-2 sm:opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                    onClick={(e) => e.stopPropagation()}
                                 >
                                    <ImageActionMenu
                                       image={image}
                                       onAction={(action, imageId) =>
                                          onImageAction?.(imageId, action)
                                       }
                                       showSetCover={showSetCover}
                                       showMoveToAlbum={showMoveToAlbum}
                                       showAddToCollection={showAddToCollection}
                                       className="bg-background/80 backdrop-blur-sm"
                                    />
                                 </div>
                              )}

                              {/* Mobile Actions - Always visible on mobile */}
                              {showActions && (
                                 <div
                                    className="absolute top-2 right-2 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity duration-200 sm:hidden"
                                    onClick={(e) => e.stopPropagation()}
                                 >
                                    <ImageActionMenu
                                       image={image}
                                       onAction={(action, imageId) =>
                                          onImageAction?.(imageId, action)
                                       }
                                       showSetCover={showSetCover}
                                       showMoveToAlbum={showMoveToAlbum}
                                       showAddToCollection={showAddToCollection}
                                       className="bg-background/80 backdrop-blur-sm"
                                    />
                                 </div>
                              )}
                           </div>
                        </div>
                     ))}
                  </div>
               ))}
            </div>

            {/* Loading indicator for infinite scroll */}
            {(hasNextPage || isFetchingNextPage) && (
               <div
                  ref={loadingRef}
                  className="flex items-center justify-center py-8"
               >
                  <div className="flex items-center space-x-2">
                     <div className="size-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                     <span className="text-sm text-muted-foreground">
                        {isFetchingNextPage
                           ? "Loading more images..."
                           : "Scroll to load more"}
                     </span>
                  </div>
               </div>
            )}

            {/* End of content indicator */}
            {!hasNextPage && images.length > 0 && (
               <div className="flex items-center justify-center pt-8">
                  <span className="text-sm text-muted-foreground">
                     You&apos;ve reached the end of the gallery
                  </span>
               </div>
            )}
         </div>

         {/* Image Modal */}
         <ImageModal
            image={selectedImage}
            isOpen={isModalOpen}
            onClose={handleCloseModal}
         />
      </>
   );
}
