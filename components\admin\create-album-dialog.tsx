"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   Di<PERSON>Title,
   DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useCreateAlbum } from "@/lib/hooks/use-albums";
import {
   CreateAlbumFormData,
   createAlbumSchema,
} from "@/lib/schemas/album-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

interface CreateAlbumDialogProps {
   trigger?: React.ReactNode;
   onSuccess?: (albumId: string) => void;
   open?: boolean;
   onOpenChange?: (open: boolean) => void;
   showTrigger?: boolean;
}

export default function CreateAlbumDialog({
   trigger,
   onSuccess,
   open: controlledOpen,
   onOpenChange: controlledOnOpenChange,
   showTrigger = false,
}: CreateAlbumDialogProps) {
   const [uncontrolledOpen, setUncontrolledOpen] = useState(false);
   const open =
      controlledOpen !== undefined ? controlledOpen : uncontrolledOpen;
   const setOpen = controlledOnOpenChange || setUncontrolledOpen;
   const router = useRouter();
   const createAlbumMutation = useCreateAlbum();

   const form = useForm({
      resolver: zodResolver(createAlbumSchema),
      defaultValues: {
         name: "",
         description: "",
         hasPassword: false,
         password: "",
      },
      mode: "onChange",
   });

   const {
      register,
      handleSubmit,
      formState: { errors, isSubmitting },
      reset,
      watch,
      setValue,
   } = form;

   const hasPassword = watch("hasPassword");

   const onSubmit = async (data: CreateAlbumFormData) => {
      try {
         // Fix: convert null coverImageUrl to undefined
         const submitData = {
            ...data,
            coverImageUrl: data.coverImageUrl ?? undefined,
         };
         const result = await createAlbumMutation.mutateAsync(submitData);

         if (result._id) {
            setOpen(false);
            reset();

            if (onSuccess) {
               onSuccess(result._id.toString());
            } else {
               // Default behavior: redirect to album detail page
               router.push(`/admin/albums/${result._id}`);
            }
         }
      } catch (error) {
         // Error is handled by the mutation's onError callback
         console.error("Failed to create album:", error);
      }
   };

   const handleOpenChange = (newOpen: boolean) => {
      setOpen(newOpen);
      if (!newOpen) {
         reset();
      }
   };

   return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
         <DialogTrigger asChild>
            {trigger ||
               (showTrigger ? (
                  <Button className="bg-gradient-accent hover:opacity-90">
                     <Plus className="w-4 h-4" />
                     Create Album
                  </Button>
               ) : null)}
         </DialogTrigger>
         <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
               <DialogTitle>Create New Album</DialogTitle>
               <DialogDescription>
                  Create a new album to organize your photos.
               </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
               <div className="space-y-3">
                  <Label htmlFor="name">Album Name *</Label>
                  <Input
                     id="name"
                     placeholder="Enter album name"
                     {...register("name")}
                     className={errors.name ? "border-destructive" : ""}
                  />
                  {errors.name && (
                     <p className="text-sm text-destructive">
                        {errors.name.message}
                     </p>
                  )}
               </div>

               <div className="space-y-3">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                     id="description"
                     placeholder="Enter album description (optional)"
                     rows={3}
                     {...register("description")}
                     className={errors.description ? "border-destructive" : ""}
                  />
                  {errors.description && (
                     <p className="text-sm text-destructive">
                        {errors.description.message}
                     </p>
                  )}
               </div>

               <div className="flex items-center space-x-2">
                  <Switch
                     id="hasPassword"
                     checked={hasPassword}
                     onCheckedChange={(checked) =>
                        setValue("hasPassword", checked as boolean)
                     }
                  />
                  <Label htmlFor="hasPassword" className="text-sm font-normal">
                     Add password
                  </Label>
               </div>

               {hasPassword && (
                  <div className="space-y-3">
                     <Label htmlFor="password">Password *</Label>
                     <Input
                        id="password"
                        type="text"
                        placeholder="Enter password"
                        {...register("password", { required: true })}
                        className={errors.password ? "border-destructive" : ""}
                     />
                     {errors.password && (
                        <p className="text-sm text-destructive">
                           {errors.password.message}
                        </p>
                     )}
                  </div>
               )}

               <DialogFooter>
                  <Button
                     type="button"
                     variant="outline"
                     onClick={() => setOpen(false)}
                     disabled={isSubmitting}
                  >
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     disabled={isSubmitting}
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     {isSubmitting ? (
                        <>
                           <Loader2 className="w-4 h-4 animate-spin" />
                           Creating...
                        </>
                     ) : (
                        "Create Album"
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}
