"use client";

import CreateAlbumDialog from "@/components/admin/create-album-dialog";
import { Button } from "@/components/ui/button";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { useAlbums, useCreateAlbum } from "@/lib/hooks/use-albums";
import { Album, FolderOpen, Plus } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface MoveToAlbumDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   imageId: string;
   currentAlbumId?: string | null;
   onMove: (imageId: string, albumId: string) => Promise<void>;
}

export default function MoveToAlbumDialog({
   open,
   onOpenChange,
   imageId,
   currentAlbumId,
   onMove,
}: MoveToAlbumDialogProps) {
   const [selectedAlbumId, setSelectedAlbumId] = useState<string>(
      currentAlbumId || ""
   );
   const [isMoving, setIsMoving] = useState(false);
   const [showCreateAlbum, setShowCreateAlbum] = useState(false);

   const { data: albumsData, isLoading: albumsLoading } = useAlbums({
      limit: 100,
   });
   const createAlbumMutation = useCreateAlbum();

   const albums = albumsData?.data || [];

   const handleMove = async () => {
      if (!selectedAlbumId) {
         toast.error("Please select an album");
         return;
      }

      try {
         setIsMoving(true);
         await onMove(imageId, selectedAlbumId);
         onOpenChange(false);
         toast.success("Image moved successfully");
      } catch (error) {
         console.error("Error moving image:", error);
         toast.error("Failed to move image");
      } finally {
         setIsMoving(false);
      }
   };

   const handleCreateAlbum = () => {
      setShowCreateAlbum(true);
   };

   const handleCreateAlbumSuccess = (albumId: string) => {
      setShowCreateAlbum(false);
      setSelectedAlbumId(albumId);
      toast.success("Album created successfully");
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <DialogTitle>Move to Album</DialogTitle>
               <DialogDescription>
                  Select which album you want to move this image to.
               </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
               {albumsLoading ? (
                  <div className="flex items-center justify-center h-32">
                     <div className="text-sm text-muted-foreground">
                        Loading albums...
                     </div>
                  </div>
               ) : albums.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                     <div className="text-center">
                        <FolderOpen className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <h3 className="text-lg font-medium mb-2 text-foreground">
                           No Albums Available
                        </h3>
                        <p className="text-sm mb-4 max-w-sm">
                           Create your first album to organize your images.
                        </p>
                        <Button
                           onClick={handleCreateAlbum}
                           className="bg-gradient-accent hover:opacity-90"
                        >
                           Create Album
                        </Button>
                     </div>
                  </div>
               ) : (
                  <div className="space-y-2">
                     <label className="text-sm font-medium">
                        Select Album:
                     </label>
                     <Select
                        value={selectedAlbumId}
                        onValueChange={setSelectedAlbumId}
                     >
                        <SelectTrigger className="w-full">
                           <SelectValue placeholder="Choose an album..." />
                        </SelectTrigger>
                        <SelectContent>
                           {albums.map((album) => (
                              <SelectItem
                                 key={album._id?.toString() || ""}
                                 value={album._id?.toString() || ""}
                                 disabled={album._id === currentAlbumId}
                              >
                                 <div className="flex items-center space-x-2">
                                    <Album className="w-4 h-4" />
                                    <span>{album.name}</span>
                                    {album._id === currentAlbumId && (
                                       <span className="text-xs text-muted-foreground">
                                          (Current)
                                       </span>
                                    )}
                                 </div>
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>

                     <Button
                        onClick={handleCreateAlbum}
                        disabled={createAlbumMutation.isPending}
                        className="w-full mt-2"
                     >
                        <Plus className="w-4 h-4" />
                        Create New Album
                     </Button>
                  </div>
               )}
            </div>

            {albums.length > 0 && (
               <DialogFooter>
                  <Button variant="outline" onClick={() => onOpenChange(false)}>
                     Cancel
                  </Button>
                  <Button
                     onClick={handleMove}
                     disabled={!selectedAlbumId || isMoving || albumsLoading}
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     {isMoving ? "Moving..." : "Move Image"}
                  </Button>
               </DialogFooter>
            )}

            <CreateAlbumDialog
               open={showCreateAlbum}
               onOpenChange={setShowCreateAlbum}
               onSuccess={handleCreateAlbumSuccess}
            />
         </DialogContent>
      </Dialog>
   );
}
