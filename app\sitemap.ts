import { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
   const baseUrl = "http://astralstudios.co.uk";
   const currentDate = new Date();

   // Static pages with their priorities and update frequencies
   const staticPages = [
      { url: "", priority: 1.0, changeFrequency: "weekly" as const },
      { url: "/about", priority: 0.8, changeFrequency: "monthly" as const },
      { url: "/services", priority: 0.9, changeFrequency: "weekly" as const },
      { url: "/portfolio", priority: 0.9, changeFrequency: "weekly" as const },
      { url: "/gallery", priority: 0.8, changeFrequency: "weekly" as const },
      { url: "/contact", priority: 0.8, changeFrequency: "monthly" as const },
   ];

   // Service pages
   const servicePages = [
      {
         url: "/services/wedding",
         priority: 0.9,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/pre-wedding",
         priority: 0.8,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/pregnancy",
         priority: 0.8,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/child-dedication",
         priority: 0.7,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/birthday-shoot",
         priority: 0.7,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/bridal-shower",
         priority: 0.7,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/videography",
         priority: 0.8,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/360-booth",
         priority: 0.7,
         changeFrequency: "monthly" as const,
      },
      {
         url: "/services/dry-ice",
         priority: 0.6,
         changeFrequency: "monthly" as const,
      },
   ];

   // Combine all pages
   const allPages = [...staticPages, ...servicePages];

   return allPages.map((page) => ({
      url: `${baseUrl}${page.url}`,
      lastModified: currentDate,
      changeFrequency: page.changeFrequency,
      priority: page.priority,
   }));
}
