import { jwtVerify } from "jose";
import { NextRequest, NextResponse } from "next/server";

// Session configuration
const SESSION_COOKIE_NAME = "admin-session";
const JWT_SECRET = new TextEncoder().encode(
   process.env.JWT_SECRET || "your-secret-key-change-this-in-production"
);

// Protected routes that require authentication
const PROTECTED_ROUTES = ["/admin"];

/**
 * Check if a path is protected
 */
function isProtectedRoute(pathname: string): boolean {
   return PROTECTED_ROUTES.some((route) => pathname.startsWith(route));
}

/**
 * Verify session token
 */
async function verifySession(token: string): Promise<boolean> {
   try {
      const { payload } = await jwtVerify(token, JWT_SECRET);

      // Check if token is expired
      const exp = payload.exp as number;
      if (exp * 1000 < Date.now()) {
         return false;
      }

      return true;
   } catch (error) {
      console.error("Error verifying session:", error);
      return false;
   }
}

export async function middleware(request: NextRequest) {
   const { pathname } = request.nextUrl;

   // Skip middleware for static files and API routes
   if (
      pathname.startsWith("/_next") ||
      pathname.startsWith("/api") ||
      pathname.includes(".")
   ) {
      return NextResponse.next();
   }

   // Get session token from cookies
   const token = request.cookies.get(SESSION_COOKIE_NAME)?.value;
   const isAuthenticated = token ? await verifySession(token) : false;

   // Handle protected routes
   if (isProtectedRoute(pathname)) {
      if (!isAuthenticated) {
         // Redirect to login if not authenticated
         const loginUrl = new URL("/login", request.url);
         loginUrl.searchParams.set("redirect", pathname);
         return NextResponse.redirect(loginUrl);
      }

      // Allow access to protected route
      return NextResponse.next();
   }

   // Handle login page
   if (pathname === "/login") {
      if (isAuthenticated) {
         // Redirect to admin dashboard if already authenticated
         const redirectTo =
            request.nextUrl.searchParams.get("redirect") || "/admin";
         return NextResponse.redirect(new URL(redirectTo, request.url));
      }

      // Allow access to login page
      return NextResponse.next();
   }

   // Allow access to all other routes
   return NextResponse.next();
}

export const config = {
   matcher: [
      /*
       * Match all request paths except for the ones starting with:
       * - api (API routes)
       * - _next/static (static files)
       * - _next/image (image optimization files)
       * - favicon.ico (favicon file)
       * - public files (images, etc.)
       */
      "/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)",
   ],
};
