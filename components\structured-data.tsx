/* eslint-disable @typescript-eslint/no-explicit-any */
interface StructuredDataProps {
   type:
      | "organization"
      | "service"
      | "localBusiness"
      | "breadcrumb"
      | "imageGallery"
      | "album"
      | "collection";
   data?: any;
}

export function StructuredData({ type, data }: StructuredDataProps) {
   const getStructuredData = () => {
      switch (type) {
         case "organization":
            return {
               "@context": "https://schema.org",
               "@type": "Organization",
               name: "Astral Studios",
               url: "http://astralstudios.co.uk",
               logo: "http://astralstudios.co.uk/astral-logo.svg",
               description:
                  "Professional photography and videography services in the UK. Specializing in weddings, pre-wedding shoots, pregnancy photography, child dedication, 360 video booth rental, and dry ice machine rental.",
               email: "<EMAIL>",
               telephone: "+447886161245",
               address: {
                  "@type": "PostalAddress",
                  addressCountry: "GB",
                  addressRegion: "United Kingdom",
               },
               sameAs: [
                  "https://instagram.com/astralstudios",
                  "https://tiktok.com/@astralstudioz",
               ],
               serviceArea: {
                  "@type": "Country",
                  name: "United Kingdom",
               },
            };

         case "localBusiness":
            return {
               "@context": "https://schema.org",
               "@type": "LocalBusiness",
               "@id": "http://astralstudios.co.uk",
               name: "Astral Studios",
               image: "http://astralstudios.co.uk/images/hero.JPG",
               description:
                  "Professional photography and videography services specializing in weddings, pre-wedding shoots, pregnancy photography, and event coverage.",
               url: "http://astralstudios.co.uk",
               telephone: "+447886161245",
               email: "<EMAIL>",
               address: {
                  "@type": "PostalAddress",
                  addressCountry: "GB",
               },
               geo: {
                  "@type": "GeoCoordinates",
                  latitude: "51.5074",
                  longitude: "-0.1278",
               },
               openingHoursSpecification: [
                  {
                     "@type": "OpeningHoursSpecification",
                     dayOfWeek: [
                        "Monday",
                        "Tuesday",
                        "Wednesday",
                        "Thursday",
                        "Friday",
                     ],
                     opens: "09:00",
                     closes: "18:00",
                  },
                  {
                     "@type": "OpeningHoursSpecification",
                     dayOfWeek: "Saturday",
                     opens: "10:00",
                     closes: "16:00",
                  },
               ],
               priceRange: "££",
               serviceArea: {
                  "@type": "Country",
                  name: "United Kingdom",
               },
            };

         case "service":
            return {
               "@context": "https://schema.org",
               "@type": "Service",
               serviceType: data?.serviceType || "Photography Services",
               provider: {
                  "@type": "Organization",
                  name: "Astral Studios",
                  url: "http://astralstudios.co.uk",
               },
               areaServed: {
                  "@type": "Country",
                  name: "United Kingdom",
               },
               description:
                  data?.description ||
                  "Professional photography and videography services",
               offers: {
                  "@type": "Offer",
                  availability: "https://schema.org/InStock",
                  price: data?.price || "Contact for pricing",
                  priceCurrency: "GBP",
               },
            };

         case "breadcrumb":
            return {
               "@context": "https://schema.org",
               "@type": "BreadcrumbList",
               itemListElement:
                  data?.items?.map((item: any, index: number) => ({
                     "@type": "ListItem",
                     position: index + 1,
                     name: item.name,
                     item: item.url,
                  })) || [],
            };

         case "imageGallery":
            return {
               "@context": "https://schema.org",
               "@type": "ImageGallery",
               name: data?.name || "Astral Studios Gallery",
               description:
                  data?.description ||
                  "Professional photography gallery featuring albums and collections",
               url: data?.url || "http://astralstudios.co.uk/gallery",
               creator: {
                  "@type": "Organization",
                  name: "Astral Studios",
                  url: "http://astralstudios.co.uk",
               },
               mainEntity:
                  data?.albums?.map((album: any) => ({
                     "@type": "ImageObject",
                     name: album.name,
                     description: album.description,
                     url: album.url,
                     contentUrl: album.coverImageUrl,
                     dateCreated: album.createdAt,
                  })) || [],
            };

         case "album":
            return {
               "@context": "https://schema.org",
               "@type": "ImageGallery",
               name: data?.name,
               description: data?.description,
               url: data?.url,
               dateCreated: data?.createdAt,
               dateModified: data?.updatedAt,
               creator: {
                  "@type": "Organization",
                  name: "Astral Studios",
                  url: "http://astralstudios.co.uk",
               },
               mainEntity:
                  data?.images?.map((image: any) => ({
                     "@type": "ImageObject",
                     name: image.name,
                     contentUrl: image.url,
                     width: image.width,
                     height: image.height,
                     encodingFormat: image.mimeType,
                     dateCreated: image.createdAt,
                  })) || [],
               numberOfItems: data?.imageCount || 0,
            };

         case "collection":
            return {
               "@context": "https://schema.org",
               "@type": "Collection",
               name: data?.name,
               description: data?.description,
               url: data?.url,
               dateCreated: data?.createdAt,
               dateModified: data?.updatedAt,
               creator: {
                  "@type": "Organization",
                  name: "Astral Studios",
                  url: "http://astralstudios.co.uk",
               },
               mainEntity:
                  data?.images?.map((image: any) => ({
                     "@type": "ImageObject",
                     name: image.name,
                     contentUrl: image.url,
                     width: image.width,
                     height: image.height,
                     encodingFormat: image.mimeType,
                     dateCreated: image.createdAt,
                  })) || [],
               numberOfItems: data?.imageCount || 0,
               additionalProperty: data?.color
                  ? {
                       "@type": "PropertyValue",
                       name: "themeColor",
                       value: data.color,
                    }
                  : undefined,
            };

         default:
            return {};
      }
   };

   const structuredData = getStructuredData();

   return (
      <script
         type="application/ld+json"
         dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
         }}
      />
   );
}
