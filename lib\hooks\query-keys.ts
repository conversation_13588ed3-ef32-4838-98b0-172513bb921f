import { AdminCommentFilters, PaginationOptions } from "@/lib/models";

/**
 * Query key factory for consistent query key generation
 */
export const queryKeys = {
   // Images
   images: {
      all: ["images"] as const,
      lists: () => [...queryKeys.images.all, "list"] as const,
      list: (
         filters: PaginationOptions & {
            albumId?: string | null;
            collectionId?: string | null;
         }
      ) => [...queryKeys.images.lists(), filters] as const,
      infiniteList: (
         filters: PaginationOptions & {
            albumId?: string | null;
            collectionId?: string | null;
         }
      ) => [...queryKeys.images.all, "infinite", filters] as const,
      ungrouped: (filters: PaginationOptions) =>
         [...queryKeys.images.all, "ungrouped", filters] as const,
      infiniteUngrouped: (filters: PaginationOptions) =>
         [...queryKeys.images.all, "infinite-ungrouped", filters] as const,
      detail: (id: string) => [...queryKeys.images.all, "detail", id] as const,
      search: (query: string, filters: PaginationOptions) =>
         [...queryKeys.images.all, "search", query, filters] as const,
      recent: (limit: number) =>
         [...queryKeys.images.all, "recent", limit] as const,
   },

   // Albums
   albums: {
      all: ["albums"] as const,
      lists: () => [...queryKeys.albums.all, "list"] as const,
      list: (filters: PaginationOptions) =>
         [...queryKeys.albums.lists(), filters] as const,
      public: (filters: PaginationOptions) =>
         [...queryKeys.albums.all, "public", filters] as const,
      detail: (id: string) => [...queryKeys.albums.all, "detail", id] as const,
   },

   // Collections
   collections: {
      all: ["collections"] as const,
      lists: () => [...queryKeys.collections.all, "list"] as const,
      list: (filters: PaginationOptions) =>
         [...queryKeys.collections.lists(), filters] as const,
      public: (filters: PaginationOptions) =>
         [...queryKeys.collections.all, "public", filters] as const,
      detail: (id: string) =>
         [...queryKeys.collections.all, "detail", id] as const,
   },

   // Comments
   comments: {
      all: ["comments"] as const,
      byAlbum: (albumId: string) =>
         [...queryKeys.comments.all, "album", albumId] as const,
      count: (albumId: string) =>
         [...queryKeys.comments.all, "count", albumId] as const,
      detail: (id: string) =>
         [...queryKeys.comments.all, "detail", id] as const,
   },

   // Admin Comments
   adminComments: {
      all: ["admin-comments"] as const,
      byAlbum: (albumId: string, filters?: AdminCommentFilters) =>
         [...queryKeys.adminComments.all, "album", albumId, filters] as const,
      countByAlbum: (albumId: string, filters?: AdminCommentFilters) =>
         [...queryKeys.adminComments.all, "count", albumId, filters] as const,
      byId: (id: string) =>
         [...queryKeys.adminComments.all, "detail", id] as const,
   },
} as const;

/**
 * Invalidation helpers
 */
export const invalidationKeys = {
   // Invalidate all images
   allImages: () => queryKeys.images.all,

   // Invalidate specific image lists
   imagesList: () => queryKeys.images.lists(),
   ungroupedImages: () => [...queryKeys.images.all, "ungrouped"],
   recentImages: () => [...queryKeys.images.all, "recent"],

   // Invalidate infinite queries
   infiniteImages: () => [...queryKeys.images.all, "infinite"],
   infiniteUngroupedImages: () => [
      ...queryKeys.images.all,
      "infinite-ungrouped",
   ],

   // Invalidate all albums
   allAlbums: () => queryKeys.albums.all,
   albumsList: () => queryKeys.albums.lists(),

   // Invalidate all collections
   allCollections: () => queryKeys.collections.all,
   collectionsList: () => queryKeys.collections.lists(),

   // Invalidate all comments
   comments: {
      all: queryKeys.comments.all,
   },

   // Invalidate all admin comments
   adminComments: {
      all: queryKeys.adminComments.all,
   },
} as const;
